#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: center;
  width: 100%;
  height: 100%;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* ===== 全局基础样式 ===== */
body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  /* 默认背景 - 仅在没有特定页面背景时显示 */
  background-color: #f9f9f9;
}

/* 专用页面背景覆盖 */
body.ai-analyzer-active {
  /* AI分析器页面使用渐变背景 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

body.ai-project-active {
  /* AI项目页面使用深色背景 */
  background-color: #050a12 !important;
}

body.main-page-active {
  /* 主页面保持默认浅色背景 */
  background-color: #f9f9f9 !important;
}

.app-container {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  /* 确保容器可以滚动 */
  overflow-x: hidden;
  overflow-y: auto;
  /* 背景继承自body，避免冲突 */
  background: inherit;
}

/* ===== 全局HTML和滚动条设置 ===== */
html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  /* 隐藏水平滚动条但保持垂直滚动功能 */
  overflow-x: hidden;
  overflow-y: auto;
}

/* 隐藏滚动条 - Webkit浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* 隐藏滚动条 - Firefox */
html {
  scrollbar-width: none;
}

/* 确保所有容器都隐藏滚动条 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* ===== 页面间平滑过渡 ===== */
.app-container {
  transition: background-color 0.3s ease;
}

/* ===== 确保特定组件不被全局样式影响 ===== */
.ai-analyzer-page,
.ai-project-page,
.main-page {
  /* 重置任何可能的继承样式 */
  text-align: initial;
  position: relative;
  z-index: 1;
}
